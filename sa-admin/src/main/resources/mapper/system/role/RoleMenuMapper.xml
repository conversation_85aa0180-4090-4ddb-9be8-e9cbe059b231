<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.system.role.dao.RoleMenuDao">
    <delete id="deleteByRoleId">
        delete
        from t_role_menu
        where role_id = #{roleId}
    </delete>
    <select id="queryMenuIdByRoleId" resultType="java.lang.Long">
        select menu_id
        from t_role_menu
        where role_id = #{roleId}
    </select>
    <select id="queryAllRoleMenu"
            resultType="net.lab1024.sa.admin.module.system.role.domain.entity.RoleMenuEntity">
        select *
        from t_role_menu
    </select>

    <select id="selectMenuListByRoleIdList"
            resultType="net.lab1024.sa.admin.module.system.menu.domain.entity.MenuEntity">
        SELECT
        distinct t_menu.*
        from t_menu
        left join t_role_menu on t_role_menu.menu_id = t_menu.menu_id
        <where>
            <if test="deletedFlag != null">
                and t_menu.deleted_flag = #{deletedFlag}
            </if>
            <if test="roleIdList != null and roleIdList.size > 0">
                and t_role_menu.role_id in
                <foreach collection="roleIdList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY t_menu.sort ASC
    </select>
</mapper>