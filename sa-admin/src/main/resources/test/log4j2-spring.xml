<?xml version="1.0" encoding="UTF-8"?>
<!--
    status : 这个用于设置log4j2自身内部的信息输出,可以不设置,当设置成trace时,会看到log4j2内部各种详细输出
    monitorInterval : Log4j能够自动检测修改配置文件和重新配置本身, 设置间隔秒数。
-->
<Configuration status="INFO" monitorInterval="30">
    <Properties>
        <Property name="log-directory" value="${sys:project.log-directory}"/>
    </Properties>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="[%d][%-5p][%t] %m (%F:%L)%n"/>
        </Console>
        <RollingFile name="debug" fileName="${log-directory}/debug/debug.log"
                     filePattern="${log-directory}/debug/debug-%d{yyyy-MM-dd}-%i.log">
            <Filters>
                <ThresholdFilter level="info" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="[%d][%-5p][%t] %m (%F:%L)%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${log-directory}/debug" maxDepth="1">
                    <IfFileName glob="debug-*.log"/>
                    <IfLastModified age="15d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
        <RollingFile name="info" fileName="${log-directory}/info/info.log"
                     filePattern="${log-directory}/info/info-%d{yyyy-MM-dd}-%i.log">
            <Filters>
                <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="[%d][%-5p][%t] %m (%F:%L)%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${log-directory}/info" maxDepth="1">
                    <IfFileName glob="info-*.log"/>
                    <IfLastModified age="15d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
        <RollingFile name="warn" fileName="${log-directory}/warn/warn.log"
                     filePattern="${log-directory}/warn/warn-%d{yyyy-MM-dd}-%i.log">
            <Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="[%d][%-5p][%t] %m (%F:%L)%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${log-directory}/warn" maxDepth="1">
                    <IfFileName glob="warn-*.log"/>
                    <IfLastModified age="15d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
        <RollingFile name="error" fileName="${log-directory}/error/error.log"
                     filePattern="${log-directory}/error/error-%d{yyyy-MM-dd}-%i.log">
            <Filters>
                <ThresholdFilter level="fatal" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="[%d][%-5p][%t] %m (%F:%L)%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${log-directory}/error" maxDepth="1">
                    <IfFileName glob="error-*.log"/>
                    <IfLastModified age="15d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
        <RollingFile name="druidSlowSqlLog" fileName="${log-directory}/slow-sql/slow-sql.log"
                     filePattern="${log-directory}/slow-sql/slow-sql-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="[%d][%-5p][%t] %m (%F:%L)%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="60">
                <Delete basePath="${log-directory}/slow-sql" maxDepth="1">
                    <IfFileName glob="slow-sql-*.log"/>
                    <IfLastModified age="30d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
    </Appenders>

    <Loggers>

        <logger name="com.alibaba.druid.filter.stat.StatFilter" level="ERROR" additivity="false">
            <AppenderRef ref="druidSlowSqlLog"/>
        </logger>

        <Root level="info">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="debug"/>
            <AppenderRef ref="info"/>
            <AppenderRef ref="warn"/>
            <AppenderRef ref="error"/>
        </Root>
    </Loggers>

</Configuration>