<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.base.module.support.feedback.dao.FeedbackDao">

    <select id="queryPage" resultType="net.lab1024.sa.base.module.support.feedback.domain.FeedbackVO">
        select *
        from t_feedback
        <where>
            <if test="query.searchWord != null and query.searchWord != '' ">
                AND (
                INSTR(feedback_content,#{query.searchWord})
                OR INSTR(user_name,#{query.searchWord})
                )
            </if>
            <if test="query.startDate != null">
                AND DATE_FORMAT(create_time, '%Y-%m-%d') &gt;= #{query.startDate}
            </if>
            <if test="query.endDate != null">
                AND DATE_FORMAT(create_time, '%Y-%m-%d') &lt;= #{query.endDate}
            </if>
        </where>
        <if test="query.sortItemList == null or query.sortItemList.size == 0">
            order by create_time desc
        </if>
    </select>
</mapper>