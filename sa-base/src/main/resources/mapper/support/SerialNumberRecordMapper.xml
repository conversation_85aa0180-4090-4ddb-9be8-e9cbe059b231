<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.base.module.support.serialnumber.dao.SerialNumberRecordDao">

    <update id="updateRecord">
        update t_serial_number_record
        set last_number = #{lastNumber},
        count = count + #{count}
        where
        serial_number_id = #{serialNumberId}
        and
        record_date = #{recordDate}
    </update>


    <select id="selectRecordIdBySerialNumberIdAndDate" resultType="java.lang.Long">
            select serial_number_record_id
            from t_serial_number_record
            where
            serial_number_id = #{serialNumberId}
            and
            record_date = #{recordDate}
    </select>

    <select id="query"
            resultType="net.lab1024.sa.base.module.support.serialnumber.domain.SerialNumberRecordEntity">
        select * from t_serial_number_record
        where serial_number_id = #{queryForm.serialNumberId}
        order by last_time desc
    </select>

</mapper>