<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.base.module.support.message.dao.MessageDao">

    <!-- 更新已读状态 -->
    <update id="updateReadFlag">
        UPDATE t_message
        SET read_flag = #{readFlag},
            read_time = now()
        WHERE message_id = #{messageId}
          AND receiver_user_type = #{receiverUserType}
          AND receiver_user_id = #{receiverUserId}
          AND read_flag != #{readFlag}
    </update>

    <!-- 分页查询消息 -->
    <select id="query" resultType="net.lab1024.sa.base.module.support.message.domain.MessageVO">
        SELECT * FROM t_message
        <where>
            <if test="query.receiverUserType != null">
                AND receiver_user_type = #{query.receiverUserType}
            </if>
            <if test="query.receiverUserId != null">
                AND receiver_user_id = #{query.receiverUserId}
            </if>
            <if test="query.messageType != null">
                AND message_type = #{query.messageType}
            </if>
            <if test="query.searchWord != null and query.searchWord !=''">
                AND ( INSTR(title,#{query.searchWord})
                OR INSTR(content,#{query.searchWord})
                )
            </if>
            <if test="query.readFlag != null">
                AND read_flag = #{query.readFlag}
            </if>
            <if test="query.startDate != null">
                AND DATE_FORMAT(create_time, '%Y-%m-%d') &gt;= DATE_FORMAT(#{query.startDate}, '%Y-%m-%d')
            </if>
            <if test="query.endDate != null">
                AND DATE_FORMAT(create_time, '%Y-%m-%d') &lt;= DATE_FORMAT(#{query.endDate}, '%Y-%m-%d')
            </if>
        </where>
        <if test="query.sortItemList == null or query.sortItemList.size == 0">
            ORDER BY message_id DESC
        </if>
    </select>

    <select id="getUnreadCount" resultType="java.lang.Long">
        SELECT count(1)
        FROM t_message
        where receiver_user_type = #{receiverUserType}
          AND receiver_user_id = #{receiverUserId}
          AND read_flag = false
    </select>
</mapper>