/**
 * ${basic.description} api 封装
 *
 * @Author:    ${basic.frontAuthor}
 * @Date:      ${basic.frontDate}
 * @Copyright  ${basic.copyright}
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const ${name.lowerCamel}Api = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/${name.lowerCamel}/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/${name.lowerCamel}/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/${name.lowerCamel}/update', param);
  },
##  ------------------  详情 ------------------

#if($deleteInfo.isSupportDetail)
  /**
   * 获取详情  <AUTHOR>
   */
  getDetail: (id) => {
      return getRequest(`/${name.lowerCamel}/getDetail/\${id}`);
  },
#end

##  ------------------  删除 ------------------
#if($deleteInfo.isSupportDelete)
    #if($deleteInfo.deleteEnum == 'Single')
  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/${name.lowerCamel}/delete/${id}`);
  },
    #end
    #if($deleteInfo.deleteEnum == 'Batch')
  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/${name.lowerCamel}/batchDelete', idList);
  },
    #end
    #if($deleteInfo.deleteEnum == 'SingleAndBatch')
  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/${name.lowerCamel}/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/${name.lowerCamel}/batchDelete', idList);
  },
    #end
#end

};
