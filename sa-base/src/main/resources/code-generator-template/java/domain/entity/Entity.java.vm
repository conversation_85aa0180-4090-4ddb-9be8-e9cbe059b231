package ${basic.javaPackageName}.domain.entity;

#foreach ($importClass in $importPackageList)
$importClass
#end

/**
 * ${basic.description} 实体类
 *
 * <AUTHOR>
 * @Date ${basic.backendDate}
 * @Copyright ${basic.copyright}
 */

@Data
@TableName("${tableName}")
public class ${name.upperCamel}Entity {
#foreach ($field in $fields)

    /**
     * $field.columnComment
     */
    #if($field.primaryKeyFlag && $field.autoIncreaseFlag)
    @TableId(type = IdType.AUTO)
    #end
    #if($field.primaryKeyFlag && !$field.autoIncreaseFlag)
    @TableId
    #end
    #if($field.columnName == "create_time")
    @TableField(fill = FieldFill.INSERT)
    #end
    #if($field.columnName == "update_time")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    #end
    private $field.javaType $field.fieldName;
#end

}
