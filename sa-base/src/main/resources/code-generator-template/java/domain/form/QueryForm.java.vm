package ${packageName};

import net.lab1024.sa.base.common.domain.PageParam;
#foreach ($importClass in $importPackageList)
$importClass
#end

/**
 * ${basic.description} 分页查询表单
 *
 * <AUTHOR>
 * @Date ${basic.backendDate}
 * @Copyright ${basic.copyright}
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class ${name.upperCamel}QueryForm extends PageParam {
#foreach ($field in $fields)

#if($field.isEnum)
    ${field.apiModelProperty}
    ${field.checkEnum}
    private $field.javaType $field.fieldName;
#end
#if(!$field.isEnum && $field.queryTypeEnum != "DateRange")
    ${field.apiModelProperty}$!{field.dict}
    private $field.javaType $field.fieldName;
#end
#if(!$field.isEnum && $field.queryTypeEnum == "DateRange")
    ${field.apiModelProperty}
    private $field.javaType ${field.fieldName}Begin;

    ${field.apiModelProperty}
    private $field.javaType ${field.fieldName}End;
#end
#end

}
