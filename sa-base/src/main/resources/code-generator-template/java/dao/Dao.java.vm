package ${packageName};

#foreach ($importClass in $importPackageList)
$importClass
#end
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * ${basic.description} Dao
 *
 * <AUTHOR>
 * @Date ${basic.backendDate}
 * @Copyright ${basic.copyright}
 */

@Mapper
public interface ${name.upperCamel}Dao extends BaseMapper<${name.upperCamel}Entity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<${name.upperCamel}VO> queryPage(Page page, @Param("queryForm") ${name.upperCamel}QueryForm queryForm);

#if($deleteInfo.isSupportDelete)
### 假删除
#if(!${deleteInfo.isPhysicallyDeleted})
#if($deleteInfo.deleteEnum == "Single" || $deleteInfo.deleteEnum == "SingleAndBatch")
    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("${primaryKeyFieldName}")${primaryKeyJavaType} ${primaryKeyFieldName},@Param("deletedFlag")boolean deletedFlag);

#end
#if($deleteInfo.deleteEnum == "Batch" || $deleteInfo.deleteEnum == "SingleAndBatch")
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<${primaryKeyJavaType}> idList,@Param("deletedFlag")boolean deletedFlag);

#end
#end
#end
}
