<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>net.lab1024</groupId>
    <artifactId>sa-parent</artifactId>
    <version>3.0.0</version>
    <packaging>pom</packaging>

    <name>sa-parent</name>
    <description>SmartAdmin project</description>

	<modules>
        <module>sa-base</module>
		<module>sa-admin</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <springboot.version>3.3.1</springboot.version>
        <spring-mock.version>2.0.8</spring-mock.version>
        <spring-security-crypto.version>6.4.3</spring-security-crypto.version>
        <mybatis-plus.version>3.5.7</mybatis-plus.version>
        <p6spy.version>3.9.1</p6spy.version>
        <knife4j.version>4.4.0</knife4j.version>
        <fastjson.version>2.0.52</fastjson.version>
        <druid.version>1.2.23</druid.version>
        <google-linkedhashmap.version>1.4.2</google-linkedhashmap.version>
        <google-guava.version>20.0</google-guava.version>
        <user-agent-utils.version>1.21</user-agent-utils.version>
        <reflections.version>0.9.11</reflections.version>
        <commons-io.version>2.15.0</commons-io.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <commons-compress.version>1.26.0</commons-compress.version>
        <commons-codec.version>1.13</commons-codec.version>
        <commons-text.version>1.9</commons-text.version>
        <xerces.version>2.12.0</xerces.version>
        <fast-excel.version>1.0.0</fast-excel.version>
        <poi.version>5.2.4</poi.version>
        <ooxml-schemas.version>1.4</ooxml-schemas.version>
        <aws-java-sdk.version>1.11.842</aws-java-sdk.version>
        <log4j-spring-boot.version>2.23.1</log4j-spring-boot.version>
        <hutool.version>5.8.29</hutool.version>
        <velocity-engine-core.version>2.3</velocity-engine-core.version>
        <jjwt.version>0.9.1</jjwt.version>
        <jwks-rsa.version>0.9.0</jwks-rsa.version>
        <velocity-tools.version>3.1</velocity-tools.version>
        <sa-token.version>1.41.0</sa-token.version>
        <ip2region.version>2.7.0</ip2region.version>
        <bcprov.version>1.80</bcprov.version>
        <jackson-datatype-jsr310.version>2.13.4</jackson-datatype-jsr310.version>
        <jackson-dataformat-yaml.version>2.16.1</jackson-dataformat-yaml.version>
        <smartdb.version>1.2.0</smartdb.version>
        <redisson.version>3.25.0</redisson.version>
        <snakeyaml.version>2.2</snakeyaml.version>
        <freemarker.version>2.3.33</freemarker.version>
        <jsoup.version>1.18.1</jsoup.version>
        <tika.version>3.1.0</tika.version>
    </properties>

    <dependencyManagement>
        <dependencies>

            <!--BOM begin-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${springboot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--BOM end-->

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-crypto</artifactId>
                <version>${spring-security-crypto.version}</version>
            </dependency>

            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.alibaba/druid-spring-boot-starter -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>com.googlecode.concurrentlinkedhashmap</groupId>
                <artifactId>concurrentlinkedhashmap-lru</artifactId>
                <version>${google-linkedhashmap.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${google-guava.version}</version>
            </dependency>

            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${user-agent-utils.version}</version>
            </dependency>

            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>${reflections.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>

            <dependency>
                <artifactId>commons-codec</artifactId>
                <groupId>commons-codec</groupId>
                <version>${commons-codec.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>jwks-rsa</artifactId>
                <version>${jwks-rsa.version}</version>
            </dependency>

            <!--velocity begin-->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity-engine-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity.tools</groupId>
                <artifactId>velocity-tools-generic</artifactId>
                <version>${velocity-tools.version}</version>
            </dependency>
            <!--velocity end-->

            <!-- Sa-Token 权限认证，在线文档：https://sa-token.cc -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>

            <!-- Sa-Token 整合 Redis （使用 jackson 序列化方式） -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-jackson</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <!-- sa-token end -->

            <!--ip 地址-->
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>${bcprov.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.idev.excel</groupId>
                <artifactId>fastexcel</artifactId>
                <version>${fast-excel.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>logback-classic</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>ooxml-schemas</artifactId>
                <version>${ooxml-schemas.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson-datatype-jsr310.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${jackson-dataformat-yaml.version}</version>
            </dependency>

            <dependency>
                <groupId>net.1024lab</groupId>
                <artifactId>smartdb</artifactId>
                <version>${smartdb.version}</version>
            </dependency>

            <!-- redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId>
                <version>${tika.version}</version>
            </dependency>

        </dependencies>

    </dependencyManagement>

    <build>
        <finalName>${project.name}-${profiles.active}-${project.version}</finalName>
        <resources>
            <resource>
                <filtering>false</filtering>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>dev/*</exclude>
                    <exclude>test/*</exclude>
                    <exclude>pre/*</exclude>
                    <exclude>prod/*</exclude>
                </excludes>
            </resource>

            <resource>
                <directory>src/main/resources/${profiles.active}</directory>
                <filtering>true</filtering>
                <includes>
                    <include>*.yaml</include>
                </includes>
            </resource>

            <resource>
                <directory>src/main/resources/${profiles.active}</directory>
                <filtering>false</filtering>
                <includes>
                    <include>*.*</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <compilerArgument>-parameters</compilerArgument>
                    <source>17</source>
                    <target>17</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${springboot.version}</version>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!--开发环境-->
        <profile>
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!--测试环境-->
        <profile>
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
            </properties>
        </profile>
        <!--预发布环境-->
        <profile>
            <id>pre</id>
            <properties>
                <profiles.active>pre</profiles.active>
            </properties>
        </profile>
        <!--生产环境-->
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
            </properties>
        </profile>
    </profiles>

</project>
